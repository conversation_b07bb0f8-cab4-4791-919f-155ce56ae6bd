<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lỗi - FreshShop</title>
    
    <!-- Bootstrap CSS -->
    <link rel="stylesheet" th:href="@{/css/bootstrap.min.css}">
    <link rel="stylesheet" th:href="@{/css/style.css}">
    <link rel="stylesheet" th:href="@{/css/custom.css}">
    
    <style>
        .error-container {
            min-height: 60vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
        }
        .error-content {
            max-width: 600px;
            padding: 2rem;
        }
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 1rem;
        }
        .error-title {
            font-size: 2rem;
            font-weight: bold;
            color: #333;
            margin-bottom: 1rem;
        }
        .error-message {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 2rem;
        }
        .error-code {
            font-size: 0.9rem;
            color: #999;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div th:replace="fragments/my-fragments :: header"></div>

    <!-- Error Content -->
    <div class="container">
        <div class="error-container">
            <div class="error-content">
                <div class="error-icon">
                    <i class="fa fa-exclamation-triangle"></i>
                </div>
                
                <h1 class="error-title">Oops! Có lỗi xảy ra</h1>
                
                <p class="error-message" th:text="${errorMessage ?: 'Có lỗi không mong muốn xảy ra. Vui lòng thử lại sau.'}">
                    Có lỗi không mong muốn xảy ra. Vui lòng thử lại sau.
                </p>
                
                <div class="d-flex justify-content-center gap-3">
                    <a href="javascript:history.back()" class="btn btn-secondary">
                        <i class="fa fa-arrow-left"></i> Quay lại
                    </a>
                    <a th:href="@{/}" class="btn btn-primary">
                        <i class="fa fa-home"></i> Trang chủ
                    </a>
                </div>
                
                <div class="error-code" th:if="${errorCode}">
                    Mã lỗi: <span th:text="${errorCode}"></span>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <div th:replace="fragments/my-fragments :: footer"></div>

    <!-- Scripts -->
    <script th:src="@{/js/jquery-3.2.1.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
</body>
</html>
