<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cửa hàng - Fresh Shop</title>
    <div th:replace="~{fragments/my-fragments::link}"/>
</head>
<body>

<!-- Header -->
<div th:replace="~{fragments/my-fragments::header(${currentPage})}"/>

<!-- Page Title -->
<div class="all-title-box">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <h2>C<PERSON>a hàng</h2>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
                    <li class="breadcrumb-item active">C<PERSON><PERSON> hàng</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Shop Content -->
<div class="shop-box-inner">
    <div class="container">
        <div class="row">
            
            <!-- Main Product Area -->
            <div class="col-xl-9 col-lg-9 col-sm-12">
                <div class="right-product-box">
                    
                    <!-- Product Count -->
                    <div class="product-item-filter row mb-4">
                        <div class="col-12">
                            <p id="productCount">
                                <span th:if="${searchTerm != null and !searchTerm.isEmpty()}">
                                    Kết quả tìm kiếm cho "<strong th:text="${searchTerm}"></strong>":
                                    <strong class="text-brand" th:text="${products != null ? products.size() : 0}"></strong> sản phẩm
                                </span>
                                <span th:unless="${searchTerm != null and !searchTerm.isEmpty()}">
                                    Hiển thị <span th:text="${products != null ? products.size() : 0}">0</span> sản phẩm
                                </span>
                            </p>
                        </div>
                    </div>

                    <!-- Products Grid -->
                    <div class="row" id="productsContainer">
                        <!-- Check if products exist -->
                        <div th:each="product : ${products}" class="col-lg-4 col-md-6 col-sm-12 mb-4">
                            <div class="products-single">

                                <!-- Product Image -->
                                <div class="box-img-hover">
                                    <!-- Debug info -->
                                    <div style="font-size: 10px; color: #999; margin-bottom: 5px;">
                                        Images: <span th:text="${product.images != null ? product.images.size() : 'null'}">0</span>
                                    </div>

                                    <!-- Try to show image with multiple fallbacks -->
                                    <div style="width: 100%; height: 250px; overflow: hidden; position: relative;">
                                        <!-- First try: if images exist and not empty -->
                                        <img th:if="${product.images != null and !product.images.empty}"
                                             th:src="${product.images[0].url}"
                                             class="img-fluid"
                                             th:alt="${product.name}"
                                             style="width: 100%; height: 100%; object-fit: cover;"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">

                                        <!-- Second try: default image -->
                                        <img th:unless="${product.images != null and !product.images.empty}"
                                             src="/images/Tao Fuji Newzealand - 1kg.jpg"
                                             class="img-fluid"
                                             th:alt="${product.name}"
                                             style="width: 100%; height: 100%; object-fit: cover;"
                                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">

                                        <!-- Final fallback: placeholder -->
                                        <div class="no-image-placeholder"
                                             style="display: none; width: 100%; height: 100%; background: #f8f9fa; align-items: center; justify-content: center; flex-direction: column; border: 2px dashed #dee2e6; position: absolute; top: 0; left: 0;">
                                            <i class="fa fa-image" style="font-size: 48px; color: #6c757d; margin-bottom: 10px;"></i>
                                            <span style="color: #6c757d;">Không có hình ảnh</span>
                                        </div>
                                    </div>

                                    <!-- Add to Cart Button -->
                                    <div class="mask-icon">
                                        <!-- Show status badge for out of stock products -->
                                        <th:block th:if="${product.status == 'PAUSED'}">
                                            <span class="status-badge out-of-stock">Hết hàng</span>
                                            <a class="cart disabled" style="cursor: not-allowed; opacity: 0.6;"
                                               onclick="showCartMessage('Sản phẩm này hiện đã hết hàng!', 'warning');">
                                                Hết hàng
                                            </a>
                                        </th:block>
                                        <!-- Normal cart functionality for active products -->
                                        <th:block th:if="${product.status == 'ACTIVE'}">
                                            <th:block th:if="${session.username != null}">
                                                <a class="cart" style="cursor: pointer"
                                                   th:onclick="'updateCart(' + ${product.id} + '); showCartMessage(\'Sản phẩm đã được thêm vào giỏ hàng!\', \'success\');'">
                                                    Thêm vào giỏ
                                                </a>
                                            </th:block>
                                            <th:block th:if="${session.username == null}">
                                                <a class="cart" style="cursor: pointer"
                                                   onclick="alert('Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng!'); window.location.href='/login';">
                                                    Thêm vào giỏ
                                                </a>
                                            </th:block>
                                        </th:block>
                                    </div>
                                </div>

                                <!-- Product Info -->
                                <div class="why-text">
                                    <h4>
                                        <a th:href="@{/product-detail(id=${product.id})}"
                                           th:text="${product.name}"
                                           style="text-decoration: none; color: inherit;">
                                            Product Name
                                        </a>
                                    </h4>
                                    <h5 th:with="hasDiscount=${product.discountedPrice != null and product.discountedPrice > 0 and product.discountedPrice != product.price}">
                                        <span th:if="${hasDiscount}" class="text-muted" style="text-decoration: line-through;" th:text="${product.price} + 'đ'"></span>
                                        <span th:if="${hasDiscount}" class="text-danger font-weight-bold" th:text="${product.discountedPrice} + 'đ'"></span>
                                        <span th:unless="${hasDiscount}" th:text="${product.price} + 'đ'">100,000đ</span>
                                    </h5>
                                </div>
                            </div>
                        </div>

                        <!-- No Products Message -->
                        <div th:if="${products == null or products.isEmpty()}" class="col-12 text-center py-5">
                            <h4>Không có sản phẩm nào</h4>
                            <p>Hiện tại chưa có sản phẩm trong cửa hàng.</p>
                        </div>
                    </div>
                    
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-xl-3 col-lg-3 col-sm-12">
                <div class="product-categori">
                    
                    <!-- Search -->
                    <div class="search-product mb-4">
                        <div class="input-group">
                            <input class="form-control" id="searchInput" placeholder="Tìm kiếm sản phẩm..." type="text"
                                   th:value="${searchTerm}" autocomplete="off">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button" onclick="performSearch()">
                                    <i class="fa fa-search"></i>
                                </button>
                                <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()"
                                        id="clearSearchBtn" style="display: none;">
                                    <i class="fa fa-times"></i>
                                </button>
                            </div>
                        </div>
                        <div id="searchSuggestions" class="search-suggestions" style="display: none;"></div>
                    </div>
                    
                    <!-- Categories -->
                    <div class="filter-sidebar-left mb-4">
                        <div class="title-left">
                            <h3>Danh mục</h3>
                        </div>
                        <div class="list-group" id="categoryList">
                            <a href="#"
                               class="list-group-item list-group-item-action category-link active"
                               data-category-id=""
                               onclick="filterByCategory(null, this); return false;">
                                Tất cả sản phẩm
                            </a>
                            <div th:if="${categories != null}">
                                <a th:each="category : ${categories}"
                                   href="#"
                                   class="list-group-item list-group-item-action category-link"
                                   th:data-category-id="${category.id}"
                                   th:text="${category.name}"
                                   th:onclick="'filterByCategory(' + ${category.id} + ', this); return false;'">
                                    Category Name
                                </a>
                            </div>
                        </div>
                    </div>
                    
                </div>
            </div>
            
        </div>
    </div>
</div>

<!-- Footer -->
<div th:replace="~{fragments/my-fragments::footer}"/>

<!-- JavaScript -->
<div th:replace="~{fragments/my-fragments::script}"/>

<script>
// Global variables
const isUserLoggedIn = /*[[${session.username != null}]]*/ false;
let searchTimeout;

// showCartMessage function is now in myjs.js - no need to duplicate here

// Search functionality
function performSearch() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    searchProducts(searchTerm);
}

function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('clearSearchBtn').style.display = 'none';
    searchProducts('');
}

function searchProducts(searchTerm) {
    console.log('searchProducts called with term:', searchTerm);

    // Show/hide clear button
    const clearBtn = document.getElementById('clearSearchBtn');
    clearBtn.style.display = searchTerm ? 'block' : 'none';

    // Show loading state
    const productsContainer = document.getElementById('productsContainer');
    const productCount = document.getElementById('productCount');

    productsContainer.innerHTML = `
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Đang tìm kiếm...</span>
            </div>
            <p class="mt-2">Đang tìm kiếm sản phẩm...</p>
        </div>
    `;

    // Reset category selection
    document.querySelectorAll('.category-link').forEach(link => {
        link.classList.remove('active');
    });
    document.querySelector('.category-link[data-category-id=""]').classList.add('active');

    // Build URL
    const url = `/api/shop/search?q=${encodeURIComponent(searchTerm)}`;

    // Make AJAX request
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update product count with search context
                if (searchTerm) {
                    productCount.innerHTML = `Kết quả tìm kiếm cho "<strong>${searchTerm}</strong>": <strong class="text-brand">${data.productCount}</strong> sản phẩm`;
                } else {
                    productCount.innerHTML = `Hiển thị ${data.productCount} sản phẩm`;
                }

                // Update products
                if (data.products && data.products.length > 0) {
                    productsContainer.innerHTML = data.products.map(product => createProductHTML(product)).join('');
                } else {
                    const message = searchTerm ?
                        `Không tìm thấy sản phẩm nào cho từ khóa "${searchTerm}"` :
                        'Hiện tại chưa có sản phẩm trong cửa hàng.';
                    productsContainer.innerHTML = `
                        <div class="col-12 text-center py-5">
                            <h4>Không có sản phẩm nào</h4>
                            <p>${message}</p>
                        </div>
                    `;
                }
            } else {
                productsContainer.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <h4>Có lỗi xảy ra</h4>
                        <p>${data.error || 'Không thể tìm kiếm sản phẩm'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            productsContainer.innerHTML = `
                <div class="col-12 text-center py-5">
                    <h4>Có lỗi xảy ra</h4>
                    <p>Không thể kết nối đến máy chủ</p>
                </div>
            `;
        });
}

// Real-time search with debouncing
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');

    // Real-time search on input
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        const searchTerm = this.value.trim();

        // Debounce search - wait 500ms after user stops typing
        searchTimeout = setTimeout(() => {
            searchProducts(searchTerm);
        }, 500);
    });

    // Search on Enter key
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault();
            clearTimeout(searchTimeout);
            performSearch();
        }
    });

    // Show clear button if there's initial search term
    if (searchInput.value.trim()) {
        document.getElementById('clearSearchBtn').style.display = 'block';
    }
});

// AJAX function to filter products by category
function filterByCategory(categoryId, clickedElement) {
    console.log('filterByCategory called with categoryId:', categoryId);

    // Show loading state
    const productsContainer = document.getElementById('productsContainer');
    const productCount = document.getElementById('productCount');

    // Add loading spinner
    productsContainer.innerHTML = `
        <div class="col-12 text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="sr-only">Đang tải...</span>
            </div>
            <p class="mt-2">Đang tải sản phẩm...</p>
        </div>
    `;

    // Update active category
    document.querySelectorAll('.category-link').forEach(link => {
        link.classList.remove('active');
    });
    clickedElement.classList.add('active');

    // Build URL
    let url = '/api/shop/products';
    if (categoryId !== null && categoryId !== undefined) {
        url += '?categoryId=' + categoryId;
    }

    console.log('Making request to:', url);

    // Make AJAX request
    fetch(url)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Update product count
                productCount.innerHTML = `Hiển thị ${data.productCount} sản phẩm`;

                // Update products
                if (data.products && data.products.length > 0) {
                    productsContainer.innerHTML = data.products.map(product => createProductHTML(product)).join('');
                } else {
                    productsContainer.innerHTML = `
                        <div class="col-12 text-center py-5">
                            <h4>Không có sản phẩm nào</h4>
                            <p>Không tìm thấy sản phẩm trong danh mục này.</p>
                        </div>
                    `;
                }
            } else {
                // Show error message
                productsContainer.innerHTML = `
                    <div class="col-12 text-center py-5">
                        <h4>Có lỗi xảy ra</h4>
                        <p>${data.error || 'Không thể tải sản phẩm'}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            productsContainer.innerHTML = `
                <div class="col-12 text-center py-5">
                    <h4>Có lỗi xảy ra</h4>
                    <p>Không thể kết nối đến máy chủ</p>
                </div>
            `;
        });
}

// Function to create product HTML
function createProductHTML(product) {
    const hasDiscount = product.discountedPrice && product.discountedPrice > 0 && product.discountedPrice !== product.price;
    const imageUrl = (product.images && product.images.length > 0) ? product.images[0].url : '/images/Tao Fuji Newzealand - 1kg.jpg';

    // Determine cart button based on product status
    let addToCartButton = '';
    let statusBadge = '';

    if (product.status === 'PAUSED') {
        statusBadge = '<span class="status-badge out-of-stock">Hết hàng</span>';
        addToCartButton = `<a class="cart disabled" style="cursor: not-allowed; opacity: 0.6;" onclick="showCartMessage('Sản phẩm này hiện đã hết hàng!', 'warning');">Hết hàng</a>`;
    } else if (product.status === 'ACTIVE') {
        addToCartButton = isUserLoggedIn ?
            `<a class="cart" style="cursor: pointer" onclick="updateCart(${product.id}); showCartMessage('Sản phẩm đã được thêm vào giỏ hàng!', 'success');">Thêm vào giỏ</a>` :
            `<a class="cart" style="cursor: pointer" onclick="showCartMessage('Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng!', 'info'); setTimeout(function() { window.location.href='/login'; }, 1500);">Thêm vào giỏ</a>`;
    } else {
        // INACTIVE products shouldn't appear in shop, but just in case
        addToCartButton = `<a class="cart disabled" style="cursor: not-allowed; opacity: 0.6;">Không khả dụng</a>`;
    }

    return `
        <div class="col-lg-4 col-md-6 col-sm-12 mb-4">
            <div class="products-single">
                <div class="box-img-hover">
                    <div style="width: 100%; height: 250px; overflow: hidden; position: relative;">
                        <img src="${imageUrl}"
                             class="img-fluid"
                             alt="${product.name}"
                             style="width: 100%; height: 100%; object-fit: cover;"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                        <div class="no-image-placeholder"
                             style="display: none; width: 100%; height: 100%; background: #f8f9fa; align-items: center; justify-content: center; flex-direction: column; border: 2px dashed #dee2e6; position: absolute; top: 0; left: 0;">
                            <i class="fa fa-image" style="font-size: 48px; color: #6c757d; margin-bottom: 10px;"></i>
                            <span style="color: #6c757d;">Không có hình ảnh</span>
                        </div>
                    </div>
                    <div class="mask-icon">
                        ${statusBadge}
                        ${addToCartButton}
                    </div>
                </div>
                <div class="why-text">
                    <h4>
                        <a href="/product-detail?id=${product.id}" style="text-decoration: none; color: inherit;">
                            ${product.name}
                        </a>
                    </h4>
                    <h5>
                        ${hasDiscount ?
                            `<span class="text-muted" style="text-decoration: line-through;">${product.price}đ</span>
                             <span class="text-danger font-weight-bold">${product.discountedPrice}đ</span>` :
                            `<span>${product.price}đ</span>`
                        }
                    </h5>
                </div>
            </div>
        </div>
    `;
}
</script>

<style>
.btn-primary {
    background-color: #b0b435;
    border-color: #b0b435;
}

.btn-primary:hover {
    background-color: #9a9e2f;
    border-color: #9a9e2f;
}

/* Product Status Badges */
.status-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
    color: white;
}

.status-badge.out-of-stock {
    background-color: #dc3545;
    color: white;
}

.status-badge.inactive {
    background-color: #dc3545;
}

/* Disabled cart button styling */
.cart.disabled {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
}

.cart.disabled:hover {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    transform: none !important;
}

.list-group-item.active {
    background-color: #b0b435;
    border-color: #b0b435;
}

/* Category link hover effects */
.category-link {
    transition: all 0.3s ease;
    cursor: pointer;
}

.category-link:hover {
    background-color: #f8f9fa;
    text-decoration: none;
}

.category-link.active {
    background-color: #b0b435 !important;
    border-color: #b0b435 !important;
    color: white !important;
}

/* Products container transition */
#productsContainer {
    transition: opacity 0.3s ease;
}

/* Loading spinner styling */
.spinner-border.text-primary {
    color: #b0b435 !important;
}

/* Product card hover effects */
.products-single {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.products-single:hover {
    transform: translateY(-5px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* Search styling */
.search-product .input-group {
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 5px;
    overflow: hidden;
}

.search-product .form-control {
    border: none;
    padding: 12px 15px;
    font-size: 14px;
}

.search-product .form-control:focus {
    box-shadow: none;
    border-color: #b0b435;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 5px 5px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
    max-height: 200px;
    overflow-y: auto;
}

.search-suggestion-item {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.search-suggestion-item:hover {
    background-color: #f8f9fa;
}

.search-suggestion-item:last-child {
    border-bottom: none;
}

/* Clear search button */
#clearSearchBtn {
    border-left: 1px solid #ddd;
}

/* Search result highlighting */
.search-highlight {
    background-color: #fff3cd;
    padding: 2px 4px;
    border-radius: 3px;
}
</style>

</body>
</html>
