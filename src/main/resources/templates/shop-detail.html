<!doctype html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
    <!--    <div th:replace="~{fragments/my-fragments.html::link}"/>-->
    <meta http-equiv="content-type" content="text/html" charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <title>Fresh Shop</title>

    <!-- Site Icons -->
    <link rel="shortcut icon" th:href="@{images/favicon.ico}" type="image/x-icon">
    <link rel="apple-touch-icon" th:href="@{images/apple-touch-icon.png}">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" th:href="@{/css/bootstrap.min.css}">

    <!-- Site CSS -->
    <link rel="stylesheet" th:href="@{css/style.css}">
    <!-- Responsive CSS -->
    <link rel="stylesheet" th:href="@{css/responsive.css}">
    <!-- Custom CSS -->
    <link rel="stylesheet" th:href="@{css/custom.css}">
</head>
<body>
<div th:replace="~{fragments/my-fragments.html::header(${currentPage})}"/>

<!-- Start All Title Box -->
<div class="all-title-box">
    <div class="container">
        <div class="row">
            <div class="col-lg-12">
                <h2>Thông tin sản phẩm</h2>
                <ul class="breadcrumb">
                    <li class="breadcrumb-item"><a href="#">Cửa hàng</a></li>
                    <li class="breadcrumb-item active">Thông tin sản phẩm</li>
                </ul>
            </div>
        </div>
    </div>
</div>
<!-- End All Title Box -->

<!-- Start Shop Detail -->
<div class="shop-detail-box-main">
    <div class="container">
        <div class="row">
            <div class="col-xl-5 col-lg-5 col-md-6">
                <div id="carousel-example-1" class="single-product-slider carousel slide" data-ride="carousel">
                    <div class="carousel-inner" role="listbox">
                        <!-- Show product images if available -->
                        <th:block th:if="${product.images != null and !product.images.empty}">
                            <th:block th:each="img, state : ${product.images}">
                                <div class="carousel-item" th:classappend="${state.index == 0} ? 'active' : ''">
                                    <img class="d-block w-100" th:src="${img.url}" th:alt="${product.name}"
                                         style="height: 400px; object-fit: cover;">
                                </div>
                            </th:block>
                        </th:block>
                        <!-- Show default image if no images available -->
                        <th:block th:if="${product.images == null or product.images.empty}">
                            <div class="carousel-item active">
                                <img class="d-block w-100" th:src="@{/images/no-image.jpg}" alt="No Image Available"
                                     style="height: 400px; object-fit: cover;">
                            </div>
                        </th:block>
                    </div>
                    <!-- Show navigation only if there are multiple images -->
                    <th:block th:if="${product.images != null and product.images.size() > 1}">
                        <a class="carousel-control-prev" href="#carousel-example-1" role="button" data-slide="prev">
                            <i class="fa fa-angle-left" aria-hidden="true"></i>
                            <span class="sr-only">Previous</span>
                        </a>
                        <a class="carousel-control-next" href="#carousel-example-1" role="button" data-slide="next">
                            <i class="fa fa-angle-right" aria-hidden="true"></i>
                            <span class="sr-only">Next</span>
                        </a>
                    </th:block>
                    <ol class="carousel-indicators" th:if="${product.images != null and !product.images.empty and product.images.size() > 1}">
                        <th:block th:each="img, state : ${product.images}">
                            <li data-target="#carousel-example-1"
                                th:data-slide-to="${state.index}"
                                th:class="${state.index == 0} ? 'active' : ''">
                                <img class="d-block w-100 img-fluid" th:src="${img.url}" th:alt="${product.name}"
                                     style="height: 60px; object-fit: cover;"/>
                            </li>
                        </th:block>
                    </ol>
                </div>
            </div>
            <div class="col-xl-7 col-lg-7 col-md-6">
                <div class="single-product-details">
                    <h2 th:text="${product.getName()}"></h2>
                    <h5 th:with="hasDiscount=${product.discountedPrice != null and product.discountedPrice > 0 and product.discountedPrice != product.price},
                                 finalPrice=${hasDiscount ? product.discountedPrice : product.price}">
                        <th:block th:if="${hasDiscount}">
                            <del th:text="${#numbers.formatInteger(product.price, 0, 'COMMA')} + 'đ'"></del>
                            <span class="text-danger font-weight-bold" th:text="${#numbers.formatInteger(product.discountedPrice, 0, 'COMMA')} + 'đ'"></span>
                        </th:block>
                        <th:block th:unless="${hasDiscount}">
                            <span class="font-weight-bold" th:text="${#numbers.formatInteger(product.price, 0, 'COMMA')} + 'đ'"></span>
                        </th:block>
                    </h5>

                    <!-- Product Status Display -->
                    <div class="product-status mb-3">
                        <h4>Tình trạng:</h4>
                        <th:block th:if="${product.status == 'ACTIVE'}">
                            <span class="badge badge-success"><i class="fa fa-check-circle"></i> Còn bán</span>
                        </th:block>
                        <th:block th:if="${product.status == 'PAUSED'}">
                            <span class="badge badge-danger"><i class="fa fa-times-circle"></i> Hết hàng</span>
                            <p class="text-danger mt-2"><i class="fa fa-exclamation-triangle"></i> Sản phẩm này hiện đã hết hàng</p>
                        </th:block>
                        <th:block th:if="${product.status == 'INACTIVE'}">
                            <span class="badge badge-secondary">Không hoạt động</span>
                        </th:block>
                    </div>

                    <h4>Mô tả:</h4>
                    <p th:text="${product.description != null ? product.description : 'Chưa có mô tả'}"></p>

                    <div class="form-group quantity-box" style="max-width: 150px;">
                        <label class="control-label" style="font-weight: 600; margin-bottom: 8px;">Số lượng</label>
                        <div class="quantity-input-wrapper" style="display: flex; align-items: center; border: 1px solid #ddd; border-radius: 4px; overflow: hidden;">
                            <button type="button" class="quantity-btn" onclick="decreaseQuantity()"
                                    style="background: #f8f9fa; border: none; padding: 8px 12px; cursor: pointer; font-size: 16px; font-weight: bold;">-</button>
                            <input id="quantity-input" class="form-control" value="1" min="1"
                                   th:max="${product.quantity != null ? product.quantity : 1}" type="number"
                                   style="border: none; text-align: center; width: 60px; padding: 8px 4px; font-weight: 600;"
                                   onchange="validateQuantity()">
                            <button type="button" class="quantity-btn" onclick="increaseQuantity()"
                                    style="background: #f8f9fa; border: none; padding: 8px 12px; cursor: pointer; font-size: 16px; font-weight: bold;">+</button>
                        </div>
                    </div>

                    <div class="price-box-bar">
                        <div class="cart-and-bay-btn">
                            <!-- Show buttons based on product status -->
                            <th:block th:if="${product.status == 'ACTIVE'}">
                                <button class="btn hvr-hover" onclick="buyNow()"
                                        th:data-product-id="${product.id}">
                                    Mua ngay
                                </button>

                                <button class="btn hvr-hover" style="color: white" onclick="addToCart()"
                                        th:data-product-id="${product.id}">
                                    Thêm vào giỏ hàng
                                </button>
                            </th:block>

                            <!-- Disabled buttons for out of stock products -->
                            <th:block th:if="${product.status == 'PAUSED'}">
                                <button class="btn btn-danger disabled" disabled
                                        style="cursor: not-allowed; opacity: 0.6;">
                                    <i class="fa fa-times-circle"></i> Hết hàng
                                </button>

                                <button class="btn btn-danger disabled" disabled
                                        style="cursor: not-allowed; opacity: 0.6;">
                                    <i class="fa fa-ban"></i> Không thể mua
                                </button>
                            </th:block>

                            <!-- Disabled buttons for inactive products -->
                            <th:block th:if="${product.status == 'INACTIVE'}">
                                <button class="btn btn-danger disabled" disabled
                                        style="cursor: not-allowed; opacity: 0.6;">
                                    Không khả dụng
                                </button>
                            </th:block>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row my-5">
            <div class="card card-outline-secondary my-4">
                <div class="card-header">
                    <h2>Đánh giá sản phẩm</h2>
                </div>
                <div class="card-body">
                    <div class="media mb-3">
                        <div class="mr-2">
                            <img class="rounded-circle border p-1"
                                 src="data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2264%22%20height%3D%2264%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2064%2064%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_160c142c97c%20text%20%7B%20fill%3Argba(255%2C255%2C255%2C.75)%3Bfont-weight%3Anormal%3Bfont-family%3AHelvetica%2C%20monospace%3Bfont-size%3A10pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_160c142c97c%22%3E%3Crect%20width%3D%2264%22%20height%3D%2264%22%20fill%3D%22%23777%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2213.5546875%22%20y%3D%2236.5%22%3E64x64%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E"
                                 alt="Generic placeholder image">
                        </div>
                        <div class="media-body">
                            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Omnis et enim aperiam
                                inventore, similique necessitatibus neque non! Doloribus, modi sapiente laboriosam
                                aperiam fugiat laborum. Sequi mollitia, necessitatibus quae sint natus.</p>
                            <small class="text-muted">Đăng bởi Anonymous on 3/1/18</small>
                        </div>
                    </div>
                    <hr>
                    <div class="media mb-3">
                        <div class="mr-2">
                            <img class="rounded-circle border p-1"
                                 src="data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2264%22%20height%3D%2264%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2064%2064%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_160c142c97c%20text%20%7B%20fill%3Argba(255%2C255%2C255%2C.75)%3Bfont-weight%3Anormal%3Bfont-family%3AHelvetica%2C%20monospace%3Bfont-size%3A10pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_160c142c97c%22%3E%3Crect%20width%3D%2264%22%20height%3D%2264%22%20fill%3D%22%23777%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2213.5546875%22%20y%3D%2236.5%22%3E64x64%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E"
                                 alt="Generic placeholder image">
                        </div>
                        <div class="media-body">
                            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Omnis et enim aperiam
                                inventore, similique necessitatibus neque non! Doloribus, modi sapiente laboriosam
                                aperiam fugiat laborum. Sequi mollitia, necessitatibus quae sint natus.</p>
                            <small class="text-muted">Đăng bởi Anonymous on 3/1/18</small>
                        </div>
                    </div>
                    <hr>
                    <div class="media mb-3">
                        <div class="mr-2">
                            <img class="rounded-circle border p-1"
                                 src="data:image/svg+xml;charset=UTF-8,%3Csvg%20width%3D%2264%22%20height%3D%2264%22%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2064%2064%22%20preserveAspectRatio%3D%22none%22%3E%3Cdefs%3E%3Cstyle%20type%3D%22text%2Fcss%22%3E%23holder_160c142c97c%20text%20%7B%20fill%3Argba(255%2C255%2C255%2C.75)%3Bfont-weight%3Anormal%3Bfont-family%3AHelvetica%2C%20monospace%3Bfont-size%3A10pt%20%7D%20%3C%2Fstyle%3E%3C%2Fdefs%3E%3Cg%20id%3D%22holder_160c142c97c%22%3E%3Crect%20width%3D%2264%22%20height%3D%2264%22%20fill%3D%22%23777%22%3E%3C%2Frect%3E%3Cg%3E%3Ctext%20x%3D%2213.5546875%22%20y%3D%2236.5%22%3E64x64%3C%2Ftext%3E%3C%2Fg%3E%3C%2Fg%3E%3C%2Fsvg%3E"
                                 alt="Generic placeholder image">
                        </div>
                        <div class="media-body">
                            <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit. Omnis et enim aperiam
                                inventore, similique necessitatibus neque non! Doloribus, modi sapiente laboriosam
                                aperiam fugiat laborum. Sequi mollitia, necessitatibus quae sint natus.</p>
                            <small class="text-muted">Đăng bởi Anonymous on 3/1/18</small>
                        </div>
                    </div>
                    <hr>
                    <a href="#" class="btn hvr-hover">Thêm đánh giá</a>
                </div>
            </div>
        </div>

        <div class="row my-5">
            <div class="col-lg-12">
                <div class="title-all text-center">
                    <h1>Sản phẩm nổi bật</h1>
                    <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed sit amet lacus enim.</p>
                </div>
                <div class="featured-products-box owl-carousel owl-theme">
                    <div class="item">
                        <div class="products-single fix">
                            <div class="box-img-hover">
                                <img src="images/img-pro-01.jpg" class="img-fluid" alt="Image">
                                <div class="mask-icon">
                                    <ul>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="Xem"><i
                                                class="fas fa-eye"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="So sánh"><i
                                                class="fas fa-sync-alt"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right"
                                               title="Thêm vào danh sách ước"><i class="far fa-heart"></i></a></li>
                                    </ul>
                                    <a class="cart" href="#">Thêm vào giỏ hàng</a>
                                </div>
                            </div>
                            <div class="why-text">
                                <h4>Lorem ipsum dolor sit amet</h4>
                                <h5> $9.79</h5>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="products-single fix">
                            <div class="box-img-hover">
                                <img src="images/img-pro-02.jpg" class="img-fluid" alt="Image">
                                <div class="mask-icon">
                                    <ul>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="Xem"><i
                                                class="fas fa-eye"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="So sánh"><i
                                                class="fas fa-sync-alt"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right"
                                               title="Thêm vào danh sách ước"><i class="far fa-heart"></i></a></li>
                                    </ul>
                                    <a class="cart" href="#">Thêm vào giỏ hàng</a>
                                </div>
                            </div>
                            <div class="why-text">
                                <h4>Lorem ipsum dolor sit amet</h4>
                                <h5> $9.79</h5>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="products-single fix">
                            <div class="box-img-hover">
                                <img src="images/img-pro-03.jpg" class="img-fluid" alt="Image">
                                <div class="mask-icon">
                                    <ul>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="Xem"><i
                                                class="fas fa-eye"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="So sánh"><i
                                                class="fas fa-sync-alt"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right"
                                               title="Thêm vào danh sách ước"><i class="far fa-heart"></i></a></li>
                                    </ul>
                                    <a class="cart" href="#">Thêm vào giỏ hàng</a>
                                </div>
                            </div>
                            <div class="why-text">
                                <h4>Lorem ipsum dolor sit amet</h4>
                                <h5> $9.79</h5>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="products-single fix">
                            <div class="box-img-hover">
                                <img src="images/img-pro-04.jpg" class="img-fluid" alt="Image">
                                <div class="mask-icon">
                                    <ul>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="Xem"><i
                                                class="fas fa-eye"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="So sánh"><i
                                                class="fas fa-sync-alt"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right"
                                               title="Thêm vào danh sách ước"><i class="far fa-heart"></i></a></li>
                                    </ul>
                                    <a class="cart" href="#">Thêm vào giỏ hàng</a>
                                </div>
                            </div>
                            <div class="why-text">
                                <h4>Lorem ipsum dolor sit amet</h4>
                                <h5> $9.79</h5>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="products-single fix">
                            <div class="box-img-hover">
                                <img src="images/img-pro-01.jpg" class="img-fluid" alt="Image">
                                <div class="mask-icon">
                                    <ul>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="Xem"><i
                                                class="fas fa-eye"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="So sánh"><i
                                                class="fas fa-sync-alt"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right"
                                               title="Thêm vào danh sách ước"><i class="far fa-heart"></i></a></li>
                                    </ul>
                                    <a class="cart" href="#">Thêm vào giỏ hàng</a>
                                </div>
                            </div>
                            <div class="why-text">
                                <h4>Lorem ipsum dolor sit amet</h4>
                                <h5> $9.79</h5>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="products-single fix">
                            <div class="box-img-hover">
                                <img src="images/img-pro-02.jpg" class="img-fluid" alt="Image">
                                <div class="mask-icon">
                                    <ul>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="Xem"><i
                                                class="fas fa-eye"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="So sánh"><i
                                                class="fas fa-sync-alt"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right"
                                               title="Thêm vào danh sách ước"><i class="far fa-heart"></i></a></li>
                                    </ul>
                                    <a class="cart" href="#">Thêm vào giỏ hàng</a>
                                </div>
                            </div>
                            <div class="why-text">
                                <h4>Lorem ipsum dolor sit amet</h4>
                                <h5> $9.79</h5>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="products-single fix">
                            <div class="box-img-hover">
                                <img src="images/img-pro-03.jpg" class="img-fluid" alt="Image">
                                <div class="mask-icon">
                                    <ul>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="Xem"><i
                                                class="fas fa-eye"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="So sánh"><i
                                                class="fas fa-sync-alt"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right"
                                               title="Thêm vào danh sách ước"><i class="far fa-heart"></i></a></li>
                                    </ul>
                                    <a class="cart" href="#">Thêm vào giỏ hàng</a>
                                </div>
                            </div>
                            <div class="why-text">
                                <h4>Lorem ipsum dolor sit amet</h4>
                                <h5> $9.79</h5>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <div class="products-single fix">
                            <div class="box-img-hover">
                                <img src="images/img-pro-04.jpg" class="img-fluid" alt="Image">
                                <div class="mask-icon">
                                    <ul>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="Xem"><i
                                                class="fas fa-eye"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right" title="So sánh"><i
                                                class="fas fa-sync-alt"></i></a></li>
                                        <li><a href="#" data-toggle="tooltip" data-placement="right"
                                               title="Thêm vào danh sách ước"><i class="far fa-heart"></i></a></li>
                                    </ul>
                                    <a class="cart" href="#">Thêm vào giỏ hàng</a>
                                </div>
                            </div>
                            <div class="why-text">
                                <h4>Lorem ipsum dolor sit amet</h4>
                                <h5> $9.79</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End Cart -->

<div th:replace="~{fragments/my-fragments.html::footer}"/>
<div th:replace="~{fragments/my-fragments.html::script}"/>

<script>
function addToCart() {
    const productId = document.querySelector('[data-product-id]').getAttribute('data-product-id');
    const quantity = document.getElementById('quantity-input').value;

    $.ajax({
        url: '/add-item',
        type: 'POST',
        data: {
            idP: productId,
            quan: quantity,
            cumulative: true
        },
        success: function(response) {
            alert('Đã thêm sản phẩm vào giỏ hàng!');
            // Update cart count in header
            updateCartCount();
        },
        error: function(xhr, status, error) {
            // Show the error message from server
            const errorMessage = xhr.responseText || 'Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng!';
            alert(errorMessage);
        }
    });
}

function buyNow() {
    const productId = document.querySelector('[data-product-id]').getAttribute('data-product-id');
    const quantity = document.getElementById('quantity-input').value;

    $.ajax({
        url: '/add-item',
        type: 'POST',
        data: {
            idP: productId,
            quan: quantity,
            cumulative: true
        },
        success: function(response) {
            window.location.href = '/cart';
        },
        error: function(xhr, status, error) {
            // Show the error message from server
            const errorMessage = xhr.responseText || 'Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng!';
            alert(errorMessage);
        }
    });
}

function updateCartCount() {
    $.get('/cart-count', function(data) {
        if (data.success) {
            $('.cart-box .cart-count').text(data.count);
        }
    });
}

// Quantity control functions
function increaseQuantity() {
    const input = document.getElementById('quantity-input');
    const currentValue = parseInt(input.value) || 1;
    const maxValue = parseInt(input.max) || 999;

    if (currentValue < maxValue) {
        input.value = currentValue + 1;
    }
}

function decreaseQuantity() {
    const input = document.getElementById('quantity-input');
    const currentValue = parseInt(input.value) || 1;
    const minValue = parseInt(input.min) || 1;

    if (currentValue > minValue) {
        input.value = currentValue - 1;
    }
}

function validateQuantity() {
    const input = document.getElementById('quantity-input');
    const value = parseInt(input.value) || 1;
    const minValue = parseInt(input.min) || 1;
    const maxValue = parseInt(input.max) || 999;

    if (value < minValue) {
        input.value = minValue;
    } else if (value > maxValue) {
        input.value = maxValue;
        alert(`Số lượng tối đa là ${maxValue}`);
    }
}
</script>

<style>
/* Quantity control styling */
.quantity-box {
    margin-bottom: 20px;
}

.quantity-input-wrapper {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: box-shadow 0.2s ease;
}

.quantity-input-wrapper:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

.quantity-btn {
    transition: background-color 0.2s ease, color 0.2s ease;
    user-select: none;
}

.quantity-btn:hover {
    background-color: #b0b435 !important;
    color: white !important;
}

.quantity-btn:active {
    transform: scale(0.95);
}

#quantity-input {
    outline: none;
    box-shadow: none;
}

#quantity-input:focus {
    outline: none;
    box-shadow: none;
}

/* Remove number input arrows */
#quantity-input::-webkit-outer-spin-button,
#quantity-input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

#quantity-input[type=number] {
    -moz-appearance: textfield;
}

/* Button styling improvements */
.cart-and-bay-btn .btn {
    padding: 12px 24px;
    font-weight: 600;
    border-radius: 4px;
    margin-right: 10px;
    transition: all 0.3s ease;
}

.cart-and-bay-btn .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}
</style>

</body>
</html>