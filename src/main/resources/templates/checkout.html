<!doctype html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
<!--  <div th:replace="~{fragments/my-fragments.html::link}"/>-->
  <meta http-equiv="content-type" content="text/html" charset="UTF-8">
  <meta name="viewport"
        content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <title>Fresh Shop</title>

  <!-- Site Icons -->
  <link rel="shortcut icon" th:href="@{images/favicon.ico}" type="image/x-icon">
  <link rel="apple-touch-icon" th:href="@{images/apple-touch-icon.png}">

  <!-- Bootstrap CSS -->
  <link rel="stylesheet" th:href="@{/css/bootstrap.min.css}">

  <!-- Site CSS -->
  <link rel="stylesheet" th:href="@{css/style.css}">
  <!-- Responsive CSS -->
  <link rel="stylesheet" th:href="@{css/responsive.css}">
  <!-- Custom CSS -->
  <link rel="stylesheet" th:href="@{css/custom.css}">
</head>
<body>
<div th:replace="~{fragments/my-fragments.html::header(${currentPage})}"/>

<!-- Start All Title Box -->
<div class="all-title-box">
  <div class="container">
    <div class="row">
      <div class="col-lg-12">
        <h2>Thanh Toán</h2>
        <ul class="breadcrumb">
          <li class="breadcrumb-item"><a href="#">Cửa Hàng</a></li>
          <li class="breadcrumb-item active">Thanh Toán</li>
        </ul>
      </div>
    </div>
  </div>
</div>
<!-- End All Title Box -->

<!-- Start Cart -->
<div class="cart-box-main">
  <div class="container">
    <form class="needs-validation d-flex" novalidate action="/checkout" method="post">
      <div class="col-sm-6 col-lg-6 mb-3">
        <div class="checkout-address">
          <div class="title-left">
            <h3>Địa chỉ thanh toán</h3>
          </div>

          <div class="row">
            <div class="col-md-6 mb-3">
              <label for="lastName">Họ *</label>
              <input name="lastName" type="text" class="form-control" id="lastName"
                     th:value="${user != null ? user.lastname : ''}"
                     placeholder="Nhập họ" required>
              <div class="invalid-feedback"> Họ hợp lệ là bắt buộc.</div>
            </div>
            <div class="col-md-6 mb-3">
              <label for="firstName">Tên *</label>
              <input name="firstName" type="text" class="form-control" id="firstName"
                     th:value="${user != null ? user.firstname : ''}"
                     placeholder="Nhập tên" required>
              <div class="invalid-feedback"> Tên hợp lệ là bắt buộc.</div>
            </div>

          </div>
          <div class="mb-3">
            <label for="phone">Số điện thoại *</label>
            <input name="phone" type="text" class="form-control" id="phone"
                   th:value="${user != null ? user.phone : ''}"
                   placeholder="Nhập số điện thoại" required>
            <div class="invalid-feedback"> Vui lòng nhập số điện thoại hợp lệ để cập nhật thông tin vận chuyển.</div>
          </div>
          <div class="mb-3">
            <label for="email">Địa chỉ email *</label>
            <input name="email" type="email" class="form-control" id="email"
                   th:value="${user != null ? user.email : ''}"
                   placeholder="Nhập địa chỉ email" required>
            <div class="invalid-feedback"> Vui lòng nhập địa chỉ email hợp lệ để cập nhật thông tin vận chuyển.</div>
          </div>
          <div class="mb-3">
            <label for="deliveryAddress">Địa chỉ giao hàng *</label>
            <input name="deliveryAddress" class="form-control" id="deliveryAddress"
                   th:value="${user != null ? (user.deliveryAddress != null and !user.deliveryAddress.isEmpty() ? user.deliveryAddress : user.address) : ''}"
                   placeholder="Nhập địa chỉ giao hàng" required>
            <div class="invalid-feedback">Vui lòng nhập địa chỉ giao hàng của bạn.</div>
          </div>
          <div class="mb-3">
            <label for="notes">Ghi chú đơn hàng</label>
            <textarea name="notes" class="form-control" id="notes" rows="3" placeholder="Ghi chú thêm cho đơn hàng (tùy chọn)"></textarea>
          </div>
          <div class="col-md-5 mb-3">
            <label for="province">Tỉnh *</label>
            <select class="wide w-100" id="province" name="province">
              <option value="An Giang">An Giang
              <option value="Bà Rịa - Vũng Tàu">Bà Rịa - Vũng Tàu
              <option value="Bắc Giang">Bắc Giang
              <option value="Bắc Kạn">Bắc Kạn
              <option value="Bạc Liêu">Bạc Liêu
              <option value="Bắc Ninh">Bắc Ninh
              <option value="Bến Tre">Bến Tre
              <option value="Bình Định">Bình Định
              <option value="Bình Dương">Bình Dương
              <option value="Bình Phước">Bình Phước
              <option value="Bình Thuận">Bình Thuận
              <option value="Bình Thuận">Bình Thuận
              <option value="Cà Mau">Cà Mau
              <option value="Cao Bằng">Cao Bằng
              <option value="Đắk Lắk">Đắk Lắk
              <option value="Đắk Nông">Đắk Nông
              <option value="Điện Biên">Điện Biên
              <option value="Đồng Nai">Đồng Nai
              <option value="Đồng Tháp">Đồng Tháp
              <option value="Đồng Tháp">Đồng Tháp
              <option value="Gia Lai">Gia Lai
              <option value="Hà Giang">Hà Giang
              <option value="Hà Nam">Hà Nam
              <option value="Hà Tĩnh">Hà Tĩnh
              <option value="Hải Dương">Hải Dương
              <option value="Hậu Giang">Hậu Giang
              <option value="company" selected="selected">
                TP.Hồ Chí Minh
              </option>
              <option value="Hòa Bình">Hòa Bình
              <option value="Hưng Yên">Hưng Yên
              <option value="Khánh Hòa">Khánh Hòa
              <option value="Kiên Giang">Kiên Giang
              <option value="Kon Tum">Kon Tum
              <option value="Lai Châu">Lai Châu
              <option value="Lâm Đồng">Lâm Đồng
              <option value="Lạng Sơn">Lạng Sơn
              <option value="Lào Cai">Lào Cai
              <option value="Long An">Long An
              <option value="Nam Định">Nam Định
              <option value="Nghệ An">Nghệ An
              <option value="Ninh Bình">Ninh Bình
              <option value="Ninh Thuận">Ninh Thuận
              <option value="Phú Thọ">Phú Thọ
              <option value="Quảng Bình">Quảng Bình
              <option value="Quảng Bình">Quảng Bình
              <option value="Quảng Ngãi">Quảng Ngãi
              <option value="Quảng Ninh">Quảng Ninh
              <option value="Quảng Trị">Quảng Trị
              <option value="Sóc Trăng">Sóc Trăng
              <option value="Sơn La">Sơn La
              <option value="Tây Ninh">Tây Ninh
              <option value="Thái Bình">Thái Bình
              <option value="Thái Nguyên">Thái Nguyên
              <option value="Thanh Hóa">Thanh Hóa
              <option value="Thừa Thiên Huế">Thừa Thiên Huế
              <option value="Tiền Giang">Tiền Giang
              <option value="Trà Vinh">Trà Vinh
              <option value="Tuyên Quang">Tuyên Quang
              <option value="Vĩnh Long">Vĩnh Long
              <option value="Vĩnh Phúc">Vĩnh Phúc
              <option value="Yên Bái">Yên Bái
              <option value="Phú Yên">Phú Yên
              <option value="Tp.Cần Thơ">Tp.Cần Thơ
              <option value="Tp.Đà Nẵng">Tp.Đà Nẵng
              <option value="Tp.Hải Phòng">Tp.Hải Phòng
              <option value="Tp.Hà Nội">Tp.Hà Nội
            </select>
          </div>

        </div>
        <hr class="mb-1">

      </div>
      <div class="col-sm-6 col-lg-6 mb-3">
        <div class="row">
          <div class="col-md-12 col-lg-12">
            <div class="shipping-method-box">
              <div class="title-left">
                <h3>Phương thức vận chuyển</h3>
              </div>
              <div class="mb-4">
                <div class="custom-control custom-radio">
                  <input id="shippingOption1" name="shipping-option" class="custom-control-input"
                         checked="checked" type="radio">
                  <label class="custom-control-label" for="shippingOption1">Giao hàng tiêu
                    chuẩn</label> <span class="float-right font-weight-bold">Miễn Phí</span>
                </div>
                <div class="ml-4 mb-2 small">(3-7 ngày làm việc)</div>
                <div class="custom-control custom-radio">
                  <input id="shippingOption2" name="shipping-option" class="custom-control-input"
                         type="radio">
                  <label class="custom-control-label" for="shippingOption2">Chuyển phát
                    nhanh</label>
                  <span class="float-right font-weight-bold">30000đ</span></div>
                <div class="ml-4 mb-2 small">(2-4 ngày làm việc)</div>
                <div class="custom-control custom-radio">
                  <input id="shippingOption3" name="shipping-option" class="custom-control-input"
                         type="radio">
                  <label class="custom-control-label" for="shippingOption3">Ngày làm việc tiếp
                    theo</label> <span class="float-right font-weight-bold">100000đ</span></div>
              </div>
            </div>
          </div>
          <div class="col-md-12 col-lg-12">
            <div class="odr-box">
              <div class="title-left">
                <h3>Giỏ hàng</h3>
              </div>
              <div class="rounded p-2 bg-light">
                <div th:each="item : ${cart}" class="media mb-2 border-bottom">
                  <div class="media-body">
                    <a href="#" th:text="${item.product.name}">Product Name</a>
                    <div class="small text-muted">
                      <span>Giá: </span>
                      <span th:if="${item.product.discountedPrice > 0 and item.product.discountedPrice != item.product.price}"
                            th:text="${item.product.discountedPrice + 'đ'}">Price</span>
                      <span th:unless="${item.product.discountedPrice > 0 and item.product.discountedPrice != item.product.price}"
                            th:text="${item.product.price + 'đ'}">Price</span>
                      <span class="mx-2">|</span>
                      <span>SL: </span><span th:text="${item.quantity}">Qty</span>
                      <span class="mx-2">|</span>
                      <span>Tổng: </span>
                      <span th:if="${item.product.discountedPrice > 0 and item.product.discountedPrice != item.product.price}"
                            th:text="${(item.product.discountedPrice * item.quantity) + 'đ'}">Subtotal</span>
                      <span th:unless="${item.product.discountedPrice > 0 and item.product.discountedPrice != item.product.price}"
                            th:text="${(item.product.price * item.quantity) + 'đ'}">Subtotal</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-12 col-lg-12">
            <div class="order-box">
              <div class="title-left">
                <h3>Đơn đặt hàng của bạn</h3>
              </div>
              <div class="d-flex">
                <div class="font-weight-bold">Sản Phẩm</div>
                <div class="ml-auto font-weight-bold">Tổng</div>
              </div>
              <hr class="my-1">
              <div class="d-flex">
                <h4>Tổng tiền hàng</h4>
                <div class="ml-auto font-weight-bold" th:text="${total + 'đ'}">0đ</div>
              </div>
              <div class="d-flex">
                <h4>Chi phí vận chuyển</h4>
                <div class="ml-auto font-weight-bold">Miễn phí</div>
              </div>
              <hr>
              <div class="d-flex gr-total">
                <h5>Tổng Cộng</h5>
                <div class="ml-auto h5" th:text="${total + 'đ'}">0đ</div>
              </div>
              <hr>
            </div>
          </div>
          <div class="col-12 d-flex shopping-box">
            <button type="submit" class="ml-auto btn hvr-hover" style="background-color: #28a745; color: white; border: none; padding: 12px 30px; border-radius: 5px;">
              Đặt hàng
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
<!-- End Cart -->

<div th:replace="~{fragments/my-fragments.html::footer}"/>



</body>
</html>