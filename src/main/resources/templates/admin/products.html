<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Quản lý sản phẩm - FreshShop Admin</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="author" content="">

    <!-- Site Icons -->
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="/images/apple-touch-icon.png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <!-- Site CSS -->
    <link rel="stylesheet" href="/css/style.css">
    <!-- Responsive CSS -->
    <link rel="stylesheet" href="/css/responsive.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/custom.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

</head>

<body>
    <!-- Start Main Top -->
    <div th:replace="~{fragments/my-fragments.html::header('admin-products')}"/>
    <!-- End Main Top -->

    <!-- Start All Title Box -->
    <div class="all-title-box">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <h2>Quản lý sản phẩm</h2>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
                        <li class="breadcrumb-item"><a href="/admin/dashboard">Admin</a></li>
                        <li class="breadcrumb-item active">Quản lý sản phẩm</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-- End All Title Box -->

    <!-- Start Product Management -->
    <div class="product-management-box-main">
        <div class="container">
            <!-- Control Panel -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fa fa-cogs mr-2"></i>
                                Bảng điều khiển sản phẩm
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <button class="btn btn-success btn-lg" onclick="showAddProductForm()">
                                        <i class="fa fa-plus"></i> Thêm sản phẩm mới
                                    </button>
                                    <button class="btn btn-info btn-lg ml-2" onclick="loadProducts()">
                                        <i class="fa fa-refresh"></i> Tải lại
                                    </button>
                                </div>
                                <div class="col-md-6 text-right">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="searchInput" placeholder="Tìm kiếm sản phẩm...">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" onclick="searchProducts()">
                                                <i class="fa fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Products Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fa fa-list mr-2"></i>
                                Danh sách sản phẩm
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="products-container">
                                <!-- Products will be loaded here via AJAX -->
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="sr-only">Đang tải...</span>
                                    </div>
                                    <p class="mt-2">Đang tải danh sách sản phẩm...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Product Form Modal -->
            <div class="modal fade" id="productModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-lg" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="productModalTitle">Thêm sản phẩm mới</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="productForm" enctype="multipart/form-data">
                                <input type="hidden" id="productId" name="productId">
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="productName">Tên sản phẩm *</label>
                                            <input type="text" class="form-control" id="productName" name="name" required>
                                            <div class="invalid-feedback" id="nameError"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="productCategory">Danh mục *</label>
                                            <select class="form-control" id="productCategory" name="categoryId" required>
                                                <option value="">Chọn danh mục</option>
                                                <th:block th:if="${categories != null}">
                                                    <option th:each="category : ${categories}"
                                                            th:value="${category.id}"
                                                            th:text="${category.name}">Category</option>
                                                </th:block>
                                            </select>
                                            <div class="invalid-feedback" id="categoryError"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="productDescription">Mô tả sản phẩm *</label>
                                    <textarea class="form-control" id="productDescription" name="description" rows="4" required></textarea>
                                    <div class="invalid-feedback" id="descriptionError"></div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="productPrice">Giá gốc (VND) *</label>
                                            <input type="number" class="form-control" id="productPrice" name="price" min="0" step="1000" required>
                                            <div class="invalid-feedback" id="priceError"></div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="productDiscountedPrice">Giá khuyến mãi (VND)</label>
                                            <input type="number" class="form-control" id="productDiscountedPrice" name="discountedPrice" min="0" step="1000">
                                            <small class="form-text text-muted">Để trống nếu không có khuyến mãi</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="productImage">Hình ảnh sản phẩm</label>
                                            <input type="file" class="form-control-file" id="productImage" name="image" accept="image/*">
                                            <small class="form-text text-muted">Chọn file ảnh (JPG, PNG, GIF)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="productActive">Trạng thái</label>
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="productActive" name="active" checked>
                                                <label class="form-check-label" for="productActive">
                                                    Kích hoạt sản phẩm
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div id="imagePreview" class="mt-3" style="display: none;">
                                    <label>Xem trước:</label>
                                    <br>
                                    <img id="previewImg" src="" alt="Preview" style="max-width: 200px; max-height: 200px;">
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                            <button type="button" class="btn btn-primary" onclick="saveProduct()">
                                <i class="fa fa-save"></i> Lưu sản phẩm
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Product Management -->

    <div th:replace="~{fragments/my-fragments.html::footer}"/>

    <!-- ALL JS FILES -->
    <script src="/js/jquery-3.2.1.min.js"></script>
    <script src="/js/popper.min.js"></script>
    <script src="/js/bootstrap.min.js"></script>
    <!-- ALL PLUGINS -->
    <script src="/js/jquery.superslides.min.js"></script>
    <script src="/js/bootstrap-select.js"></script>
    <script src="/js/inewsticker.js"></script>
    <script src="/js/bootsnav.js"></script>
    <script src="/js/images-loded.min.js"></script>
    <script src="/js/isotope.min.js"></script>
    <script src="/js/owl.carousel.min.js"></script>
    <script src="/js/baguetteBox.min.js"></script>
    <script src="/js/form-validator.min.js"></script>
    <script src="/js/contact-form-script.js"></script>
    <script src="/js/custom.js"></script>

    <!-- Product Management JavaScript -->
    <script>
        let currentProducts = [];
        let isEditMode = false;

        // Load products on page load
        $(document).ready(function() {
            loadProducts();
            
            // Image preview
            $('#productImage').change(function() {
                previewImage(this);
            });
        });

        // Load all products
        function loadProducts() {
            $('#products-container').html(`
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Đang tải...</span>
                    </div>
                    <p class="mt-2">Đang tải danh sách sản phẩm...</p>
                </div>
            `);

            fetch('/admin/products/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentProducts = data.products;
                        displayProducts(data.products);
                    } else {
                        showError('Không thể tải danh sách sản phẩm: ' + (data.error || 'Lỗi không xác định'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('Có lỗi xảy ra khi tải danh sách sản phẩm');
                });
        }

        // Display products in table
        function displayProducts(products) {
            if (products.length === 0) {
                $('#products-container').html(`
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle"></i>
                        Chưa có sản phẩm nào. <a href="#" onclick="showAddProductForm()" class="alert-link">Thêm sản phẩm đầu tiên</a>
                    </div>
                `);
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>ID</th>
                                <th>Hình ảnh</th>
                                <th>Tên sản phẩm</th>
                                <th>Danh mục</th>
                                <th>Giá gốc</th>
                                <th>Giá KM</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            products.forEach(product => {
                let statusBadge = '';
                switch(product.status) {
                    case 'ACTIVE':
                        statusBadge = '<span class="badge badge-success"><i class="fa fa-check-circle"></i> Còn bán</span>';
                        break;
                    case 'PAUSED':
                        statusBadge = '<span class="badge badge-danger"><i class="fa fa-times-circle"></i> Hết hàng</span>';
                        break;
                    case 'INACTIVE':
                        statusBadge = '<span class="badge badge-secondary"><i class="fa fa-ban"></i> Không hoạt động</span>';
                        break;
                    default:
                        statusBadge = '<span class="badge badge-secondary">' + product.status + '</span>';
                }

                const discountedPrice = product.discountedPrice && product.discountedPrice !== product.price
                    ? formatCurrency(product.discountedPrice)
                    : '-';

                html += `
                    <tr>
                        <td>${product.id}</td>
                        <td>
                            ${product.images && product.images.length > 0
                                ? `<img src="${product.images[0].url}" alt="${product.name}" style="width: 50px; height: 50px; object-fit: cover;">`
                                : '<i class="fa fa-image text-muted" style="font-size: 30px;"></i>'
                            }
                        </td>
                        <td>
                            <strong>${product.name}</strong>
                            <br>
                            <small class="text-muted">${truncateText(product.description, 50)}</small>
                        </td>
                        <td>${product.category ? product.category.name : 'Không xác định'}</td>
                        <td>${formatCurrency(product.price)}</td>
                        <td>${discountedPrice}</td>
                        <td>${statusBadge}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-info" onclick="editProduct(${product.id})" title="Chỉnh sửa">
                                    <i class="fa fa-edit"></i>
                                </button>
                                <button class="btn btn-sm ${getStatusButtonClass(product.status)}"
                                        onclick="toggleProductStatus(${product.id})"
                                        title="${getStatusButtonTitle(product.status)}">
                                    <i class="fa fa-${getStatusButtonIcon(product.status)}"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deleteProduct(${product.id})" title="Xóa">
                                    <i class="fa fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            $('#products-container').html(html);
        }

        // Show add product form
        function showAddProductForm() {
            isEditMode = false;
            $('#productModalTitle').text('Thêm sản phẩm mới');
            $('#productForm')[0].reset();
            $('#productId').val('');
            $('#imagePreview').hide();
            clearErrors();
            $('#productModal').modal('show');
        }

        // Edit product
        function editProduct(id) {
            isEditMode = true;
            $('#productModalTitle').text('Chỉnh sửa sản phẩm');

            fetch(`/admin/products/${id}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const product = data.product;
                        $('#productId').val(product.id);
                        $('#productName').val(product.name);
                        $('#productDescription').val(product.description);
                        $('#productPrice').val(product.price);
                        $('#productDiscountedPrice').val(product.discountedPrice !== product.price ? product.discountedPrice : '');
                        $('#productCategory').val(product.category ? product.category.id : '');
                        $('#productActive').prop('checked', product.status === 'ACTIVE');

                        if (product.images && product.images.length > 0) {
                            $('#previewImg').attr('src', product.images[0].url);
                            $('#imagePreview').show();
                        } else {
                            $('#imagePreview').hide();
                        }

                        clearErrors();
                        $('#productModal').modal('show');
                    } else {
                        showError('Không thể tải thông tin sản phẩm: ' + (data.error || 'Lỗi không xác định'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('Có lỗi xảy ra khi tải thông tin sản phẩm');
                });
        }

        // Save product (add or update)
        function saveProduct() {
            const formData = new FormData();
            const productId = $('#productId').val();

            formData.append('name', $('#productName').val());
            formData.append('description', $('#productDescription').val());
            formData.append('price', $('#productPrice').val());
            formData.append('discountedPrice', $('#productDiscountedPrice').val() || '0');
            formData.append('categoryId', $('#productCategory').val());
            formData.append('active', $('#productActive').is(':checked'));

            const imageFile = $('#productImage')[0].files[0];
            if (imageFile) {
                formData.append('image', imageFile);
            }

            const url = isEditMode ? `/admin/products/${productId}/update` : '/admin/products/add';

            fetch(url, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    $('#productModal').modal('hide');
                    loadProducts(); // Reload products list
                } else {
                    if (data.errors) {
                        displayErrors(data.errors);
                    } else {
                        showError(data.error || 'Có lỗi xảy ra khi lưu sản phẩm');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Có lỗi xảy ra khi lưu sản phẩm');
            });
        }

        // Helper functions for status button display
        function getStatusButtonClass(status) {
            switch(status) {
                case 'ACTIVE': return 'btn-warning';
                case 'PAUSED': return 'btn-success';
                case 'INACTIVE': return 'btn-info';
                default: return 'btn-secondary';
            }
        }

        function getStatusButtonTitle(status) {
            switch(status) {
                case 'ACTIVE': return 'Đánh dấu hết hàng';
                case 'PAUSED': return 'Ẩn sản phẩm';
                case 'INACTIVE': return 'Đưa lên bán';
                default: return 'Thay đổi trạng thái';
            }
        }

        function getStatusButtonIcon(status) {
            switch(status) {
                case 'ACTIVE': return 'times-circle';
                case 'PAUSED': return 'ban';
                case 'INACTIVE': return 'check-circle';
                default: return 'question';
            }
        }

        // Toggle product status
        function toggleProductStatus(id) {
            fetch(`/admin/products/${id}/toggle-status`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadProducts(); // Reload products list
                } else {
                    showError(data.error || 'Có lỗi xảy ra khi cập nhật trạng thái');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Có lỗi xảy ra khi cập nhật trạng thái');
            });
        }

        // Delete product
        function deleteProduct(id) {
            if (!confirm('Bạn có chắc chắn muốn xóa sản phẩm này? Hành động này không thể hoàn tác.')) {
                return;
            }

            fetch(`/admin/products/${id}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadProducts(); // Reload products list
                } else {
                    showError(data.error || 'Có lỗi xảy ra khi xóa sản phẩm');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('Có lỗi xảy ra khi xóa sản phẩm');
            });
        }

        // Search products
        function searchProducts() {
            const searchTerm = $('#searchInput').val().toLowerCase();
            if (!searchTerm) {
                displayProducts(currentProducts);
                return;
            }

            const filteredProducts = currentProducts.filter(product =>
                product.name.toLowerCase().includes(searchTerm) ||
                product.description.toLowerCase().includes(searchTerm)
            );

            displayProducts(filteredProducts);
        }

        // Preview image
        function previewImage(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    $('#previewImg').attr('src', e.target.result);
                    $('#imagePreview').show();
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Utility functions
        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        }

        function truncateText(text, maxLength) {
            return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
        }

        function getCategoryName(categoryId) {
            // This should be populated from the categories data
            const categories = {
                1: 'Rau củ quả',
                2: 'Trái cây',
                3: 'Thịt cá',
                4: 'Đồ uống'
            };
            return categories[categoryId] || 'Không xác định';
        }

        function showSuccess(message) {
            // You can implement a toast notification here
            alert('Thành công: ' + message);
        }

        function showError(message) {
            // You can implement a toast notification here
            alert('Lỗi: ' + message);
        }

        function displayErrors(errors) {
            clearErrors();
            Object.keys(errors).forEach(field => {
                const errorElement = document.getElementById(field + 'Error');
                if (errorElement) {
                    errorElement.textContent = errors[field];
                    errorElement.style.display = 'block';
                }
            });
        }

        function clearErrors() {
            document.querySelectorAll('.invalid-feedback').forEach(element => {
                element.textContent = '';
                element.style.display = 'none';
            });
        }

        // Search on Enter key
        $('#searchInput').keypress(function(e) {
            if (e.which === 13) {
                searchProducts();
            }
        });
    </script>
</body>
</html>
