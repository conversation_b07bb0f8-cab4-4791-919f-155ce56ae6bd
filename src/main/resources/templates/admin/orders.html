<!DOCTYPE html>
<html lang="vi" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Quản lý đơn hàng - FreshShop Admin</title>
    <meta name="keywords" content="">
    <meta name="description" content="">
    <meta name="author" content="">

    <!-- Site Icons -->
    <link rel="shortcut icon" href="/images/favicon.ico" type="image/x-icon">
    <link rel="apple-touch-icon" href="/images/apple-touch-icon.png">

    <!-- Bootstrap CSS -->
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <!-- Site CSS -->
    <link rel="stylesheet" href="/css/style.css">
    <!-- Responsive CSS -->
    <link rel="stylesheet" href="/css/responsive.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="/css/custom.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

</head>

<body>
    <!-- Start Main Top -->
    <div th:replace="~{fragments/my-fragments.html::header('admin-orders')}"/>
    <!-- End Main Top -->

    <!-- Start All Title Box -->
    <div class="all-title-box">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <h2>Quản lý đơn hàng</h2>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
                        <li class="breadcrumb-item"><a href="/admin/dashboard">Admin</a></li>
                        <li class="breadcrumb-item active">Quản lý đơn hàng</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <!-- End All Title Box -->

    <!-- Start Order Management -->
    <div class="order-management-box-main">
        <div class="container">
            <!-- Statistics Cards -->
            <div class="row mb-4" id="statistics-cards">
                <!-- Statistics will be loaded here -->
            </div>

            <!-- Control Panel -->
            <div class="row mb-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">
                                <i class="fa fa-filter mr-2"></i>
                                Bộ lọc đơn hàng
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-3">
                                    <select class="form-control" id="statusFilter">
                                        <option value="">Tất cả trạng thái</option>
                                        <option value="PENDING">Chờ xử lý</option>
                                        <option value="CONFIRMED">Đã xác nhận</option>
                                        <option value="SHIPPING">Đang giao hàng</option>
                                        <option value="DELIVERED">Đã giao hàng</option>
                                        <option value="CANCELLED">Đã hủy</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <select class="form-control" id="paymentFilter">
                                        <option value="">Tất cả thanh toán</option>
                                        <option value="PENDING">Chờ thanh toán</option>
                                        <option value="PAID">Đã thanh toán</option>
                                        <option value="FAILED">Thanh toán thất bại</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <div class="input-group">
                                        <input type="text" class="form-control" id="searchInput" placeholder="Tìm kiếm đơn hàng...">
                                        <div class="input-group-append">
                                            <button class="btn btn-outline-secondary" onclick="searchOrders()">
                                                <i class="fa fa-search"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-2">
                                    <button class="btn btn-info btn-block" onclick="loadOrders()">
                                        <i class="fa fa-refresh"></i> Tải lại
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Orders Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fa fa-list mr-2"></i>
                                Danh sách đơn hàng
                            </h5>
                        </div>
                        <div class="card-body">
                            <div id="orders-container">
                                <!-- Orders will be loaded here via AJAX -->
                                <div class="text-center">
                                    <div class="spinner-border" role="status">
                                        <span class="sr-only">Đang tải...</span>
                                    </div>
                                    <p class="mt-2">Đang tải danh sách đơn hàng...</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Details Modal -->
            <div class="modal fade" id="orderModal" tabindex="-1" role="dialog">
                <div class="modal-dialog modal-xl order-detail-modal" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="orderModalTitle">Chi tiết đơn hàng</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body" id="orderModalBody">
                            <!-- Order details will be loaded here -->
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Edit Status Modal -->
            <div class="modal fade" id="editStatusModal" tabindex="-1" role="dialog">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Chỉnh sửa trạng thái đơn hàng</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="editStatusForm">
                                <input type="hidden" id="editOrderId">

                                <div class="form-group">
                                    <label for="orderStatus">Trạng thái đơn hàng:</label>
                                    <select class="form-control" id="orderStatus">
                                        <option value="PENDING">Chờ xử lý</option>
                                        <option value="CONFIRMED">Đã xác nhận</option>
                                        <option value="SHIPPING">Đang giao hàng</option>
                                        <option value="DELIVERED">Đã giao hàng</option>
                                        <option value="CANCELLED">Đã hủy</option>
                                    </select>
                                </div>

                                <div class="form-group">
                                    <label for="paymentStatus">Trạng thái thanh toán:</label>
                                    <select class="form-control" id="paymentStatus">
                                        <option value="PENDING">Chờ thanh toán</option>
                                        <option value="PAID">Đã thanh toán</option>
                                        <option value="FAILED">Thanh toán thất bại</option>
                                    </select>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                            <button type="button" class="btn btn-primary" onclick="saveStatusChanges()">Lưu thay đổi</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- End Order Management -->

    <div th:replace="~{fragments/my-fragments.html::footer}"/>

    <!-- ALL JS FILES -->
    <script src="/js/jquery-3.2.1.min.js"></script>
    <script src="/js/popper.min.js"></script>
    <script src="/js/bootstrap.min.js"></script>
    <!-- ALL PLUGINS -->
    <script src="/js/jquery.superslides.min.js"></script>
    <script src="/js/bootstrap-select.js"></script>
    <script src="/js/inewsticker.js"></script>
    <script src="/js/bootsnav.js"></script>
    <script src="/js/images-loded.min.js"></script>
    <script src="/js/isotope.min.js"></script>
    <script src="/js/owl.carousel.min.js"></script>
    <script src="/js/baguetteBox.min.js"></script>
    <script src="/js/form-validator.min.js"></script>
    <script src="/js/contact-form-script.js"></script>
    <script src="/js/custom.js"></script>

    <!-- Order Management JavaScript -->
    <script>
        let currentOrders = [];

        // Load orders on page load
        $(document).ready(function() {
            loadOrders();
            
            // Filter change events
            $('#statusFilter, #paymentFilter').change(function() {
                filterOrders();
            });
        });

        // Load all orders
        function loadOrders() {
            $('#orders-container').html(`
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Đang tải...</span>
                    </div>
                    <p class="mt-2">Đang tải danh sách đơn hàng...</p>
                </div>
            `);

            fetch('/admin/orders/list')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        currentOrders = data.orders;
                        displayOrders(data.orders);
                        displayStatistics(data.statistics);
                    } else {
                        showError('Không thể tải danh sách đơn hàng: ' + (data.error || 'Lỗi không xác định'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showError('Có lỗi xảy ra khi tải danh sách đơn hàng');
                });
        }

        // Display statistics
        function displayStatistics(stats) {
            const html = `
                <div class="col-md-3">
                    <div class="card text-center bg-warning text-white">
                        <div class="card-body">
                            <h5 class="card-title">Chờ xử lý</h5>
                            <h3>${stats.pending}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center bg-info text-white">
                        <div class="card-body">
                            <h5 class="card-title">Đã xác nhận</h5>
                            <h3>${stats.confirmed}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title">Đang giao</h5>
                            <h3>${stats.shipping}</h3>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title">Doanh thu</h5>
                            <h3>${formatCurrency(stats.totalRevenue)}</h3>
                        </div>
                    </div>
                </div>
            `;
            $('#statistics-cards').html(html);
        }

        // Display orders in table
        function displayOrders(orders) {
            if (orders.length === 0) {
                $('#orders-container').html(`
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle"></i>
                        Không có đơn hàng nào.
                    </div>
                `);
                return;
            }

            let html = `
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="thead-dark">
                            <tr>
                                <th>Mã đơn</th>
                                <th>Khách hàng</th>
                                <th>Ngày đặt</th>
                                <th>Tổng tiền</th>
                                <th>Trạng thái</th>
                                <th>Thanh toán</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            orders.forEach(order => {
                const orderDate = new Date(order.orderDate).toLocaleDateString('vi-VN');
                const statusBadge = getStatusBadge(order.status);
                const paymentBadge = getPaymentStatusBadge(order.paymentStatus);

                html += `
                    <tr>
                        <td><strong>#${order.id}</strong></td>
                        <td>${order.user ? order.user.firstname + ' ' + order.user.lastname : 'N/A'}</td>
                        <td>${orderDate}</td>
                        <td>${formatCurrency(order.totalAmount)}</td>
                        <td>${statusBadge}</td>
                        <td>${paymentBadge}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-info" onclick="viewOrderDetails(${order.id})" title="Xem chi tiết">
                                    <i class="fa fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-warning" onclick="showEditStatusModal(${order.id}, '${order.status}', '${order.paymentStatus}')" title="Chỉnh sửa trạng thái">
                                    <i class="fa fa-cog"></i>
                                </button>
                                ${order.status === 'CANCELLED' ? `
                                    <button class="btn btn-sm btn-danger" onclick="deleteOrder(${order.id})" title="Xóa đơn hàng đã hủy">
                                        <i class="fa fa-trash"></i>
                                    </button>
                                ` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;

            $('#orders-container').html(html);

            // Initialize Bootstrap dropdowns after content is loaded
            setTimeout(function() {
                $('[data-toggle="dropdown"]').dropdown();
            }, 100);
        }

        // Utility functions
        function getStatusBadge(status) {
            const badges = {
                'PENDING': '<span class="badge badge-warning">Chờ xử lý</span>',
                'CONFIRMED': '<span class="badge badge-info">Đã xác nhận</span>',
                'SHIPPING': '<span class="badge badge-primary">Đang giao hàng</span>',
                'DELIVERED': '<span class="badge badge-success">Đã giao hàng</span>',
                'CANCELLED': '<span class="badge badge-danger">Đã hủy</span>'
            };
            return badges[status] || `<span class="badge badge-secondary">${status}</span>`;
        }

        function getPaymentStatusBadge(status) {
            const badges = {
                'PENDING': '<span class="badge badge-warning">Chờ thanh toán</span>',
                'PAID': '<span class="badge badge-success">Đã thanh toán</span>',
                'FAILED': '<span class="badge badge-danger">Thất bại</span>'
            };
            return badges[status] || `<span class="badge badge-secondary">${status}</span>`;
        }

        function formatCurrency(amount) {
            return new Intl.NumberFormat('vi-VN', {
                style: 'currency',
                currency: 'VND'
            }).format(amount);
        }

        function showError(message) {
            $('#orders-container').html(`
                <div class="alert alert-danger">
                    <i class="fa fa-exclamation-triangle"></i> ${message}
                </div>
            `);
        }

        function showSuccess(message) {
            alert('Thành công: ' + message);
        }

        // Filter and search functions
        function filterOrders() {
            const statusFilter = $('#statusFilter').val();
            const paymentFilter = $('#paymentFilter').val();
            
            let filteredOrders = currentOrders;
            
            if (statusFilter) {
                filteredOrders = filteredOrders.filter(order => order.status === statusFilter);
            }
            
            if (paymentFilter) {
                filteredOrders = filteredOrders.filter(order => order.paymentStatus === paymentFilter);
            }
            
            displayOrders(filteredOrders);
        }

        function searchOrders() {
            const searchTerm = $('#searchInput').val().toLowerCase();
            if (!searchTerm) {
                filterOrders();
                return;
            }

            const filteredOrders = currentOrders.filter(order => 
                order.id.toString().includes(searchTerm) ||
                (order.user && (order.user.firstname + ' ' + order.user.lastname).toLowerCase().includes(searchTerm))
            );

            displayOrders(filteredOrders);
        }

        // Order management functions
        function viewOrderDetails(orderId) {
            fetch(`/admin/orders/${orderId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        displayOrderDetails(data.order);
                    } else {
                        alert('Lỗi: ' + (data.error || 'Không thể tải chi tiết đơn hàng'));
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Có lỗi xảy ra khi tải chi tiết đơn hàng');
                });
        }

        function displayOrderDetails(order) {
            const orderDate = new Date(order.orderDate).toLocaleDateString('vi-VN');
            let html = `
                <div class="row mb-4">
                    <div class="col-lg-4">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fa fa-shopping-cart mr-2"></i>Thông tin đơn hàng</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-borderless">
                                    <tr><td><strong>Mã đơn:</strong></td><td>#${order.id}</td></tr>
                                    <tr><td><strong>Ngày đặt:</strong></td><td>${orderDate}</td></tr>
                                    <tr><td><strong>Trạng thái:</strong></td><td>${getStatusBadge(order.status)}</td></tr>
                                    <tr><td><strong>Thanh toán:</strong></td><td>${getPaymentStatusBadge(order.paymentStatus)}</td></tr>
                                    <tr><td><strong>Tổng tiền:</strong></td><td><strong class="text-success">${formatCurrency(order.totalAmount)}</strong></td></tr>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-8">
                        <div class="card h-100">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="fa fa-user mr-2"></i>Thông tin khách hàng</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr><td><strong>Tên:</strong></td><td>${order.user ? order.user.firstname + ' ' + order.user.lastname : 'N/A'}</td></tr>
                                            <tr><td><strong>Email:</strong></td><td>${order.user ? order.user.email : 'N/A'}</td></tr>
                                            <tr><td><strong>Số điện thoại:</strong></td><td>${order.phone || 'Không có'}</td></tr>
                                        </table>
                                    </div>
                                    <div class="col-md-6">
                                        <table class="table table-borderless">
                                            <tr><td><strong>Địa chỉ giao hàng:</strong></td><td>${order.deliveryAddress || 'Không có'}</td></tr>
                                            <tr><td><strong>Phương thức thanh toán:</strong></td><td>${order.paymentMethod || 'COD'}</td></tr>
                                            <tr><td><strong>Ghi chú:</strong></td><td>${order.notes || 'Không có'}</td></tr>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fa fa-list mr-2"></i>Sản phẩm đã đặt</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th style="width: 50%">Sản phẩm</th>
                                        <th style="width: 15%" class="text-center">Số lượng</th>
                                        <th style="width: 20%" class="text-right">Đơn giá</th>
                                        <th style="width: 15%" class="text-right">Thành tiền</th>
                                    </tr>
                                </thead>
                                <tbody>
            `;

            if (order.orderItems && order.orderItems.length > 0) {
                order.orderItems.forEach(item => {
                    const subtotal = item.price * item.quantity;
                    html += `
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <strong>${item.product ? item.product.name : 'Sản phẩm không xác định'}</strong>
                                        ${item.product && item.product.description ? `<br><small class="text-muted">${item.product.description.substring(0, 100)}...</small>` : ''}
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="badge badge-primary">${item.quantity}</span>
                            </td>
                            <td class="text-right">${formatCurrency(item.price)}</td>
                            <td class="text-right"><strong class="text-success">${formatCurrency(subtotal)}</strong></td>
                        </tr>
                    `;
                });

                // Add total row
                html += `
                    <tr class="table-info">
                        <td colspan="3" class="text-right"><strong>Tổng cộng:</strong></td>
                        <td class="text-right"><strong class="text-success h5">${formatCurrency(order.totalAmount)}</strong></td>
                    </tr>
                `;
            } else {
                html += `
                    <tr>
                        <td colspan="4" class="text-center text-muted py-4">
                            <i class="fa fa-inbox fa-2x mb-2"></i><br>
                            Không có sản phẩm nào trong đơn hàng này
                        </td>
                    </tr>
                `;
            }

            html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;

            $('#orderModalBody').html(html);
            $('#orderModal').modal('show');
        }

        function updateOrderStatus(orderId, status) {
            if (!confirm('Bạn có chắc chắn muốn cập nhật trạng thái đơn hàng?')) {
                return;
            }

            fetch(`/admin/orders/${orderId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `status=${status}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadOrders();
                } else {
                    alert('Lỗi: ' + (data.error || 'Không thể cập nhật trạng thái'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi cập nhật trạng thái');
            });
        }

        function updatePaymentStatus(orderId, paymentStatus) {
            if (!confirm('Bạn có chắc chắn muốn cập nhật trạng thái thanh toán?')) {
                return;
            }

            fetch(`/admin/orders/${orderId}/payment-status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `paymentStatus=${paymentStatus}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadOrders();
                } else {
                    alert('Lỗi: ' + (data.error || 'Không thể cập nhật trạng thái thanh toán'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi cập nhật trạng thái thanh toán');
            });
        }

        // Show edit status modal
        function showEditStatusModal(orderId, currentStatus, currentPaymentStatus) {
            $('#editOrderId').val(orderId);
            $('#orderStatus').val(currentStatus);
            $('#paymentStatus').val(currentPaymentStatus);
            $('#editStatusModal').modal('show');
        }

        // Save status changes
        function saveStatusChanges() {
            const orderId = $('#editOrderId').val();
            const newStatus = $('#orderStatus').val();
            const newPaymentStatus = $('#paymentStatus').val();

            if (!orderId) {
                alert('Lỗi: Không tìm thấy ID đơn hàng');
                return;
            }

            // Update order status
            fetch(`/admin/orders/${orderId}/status`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `status=${newStatus}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update payment status
                    return fetch(`/admin/orders/${orderId}/payment-status`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: `paymentStatus=${newPaymentStatus}`
                    });
                } else {
                    throw new Error(data.error || 'Không thể cập nhật trạng thái đơn hàng');
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    $('#editStatusModal').modal('hide');
                    showSuccess('Cập nhật trạng thái đơn hàng thành công');
                    loadOrders();
                } else {
                    throw new Error(data.error || 'Không thể cập nhật trạng thái thanh toán');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Lỗi: ' + error.message);
            });
        }

        // Delete cancelled order
        function deleteOrder(orderId) {
            if (!confirm('Bạn có chắc chắn muốn xóa đơn hàng đã hủy này không? Hành động này không thể hoàn tác.')) {
                return;
            }

            fetch(`/admin/orders/${orderId}/delete`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showSuccess(data.message);
                    loadOrders();
                } else {
                    alert('Lỗi: ' + (data.error || 'Không thể xóa đơn hàng'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Có lỗi xảy ra khi xóa đơn hàng');
            });
        }

        // Search on Enter key
        $('#searchInput').keypress(function(e) {
            if (e.which === 13) {
                searchOrders();
            }
        });
    </script>
</body>
</html>
