
/** ADD YOUR AWESOME CODES HERE **/

/* Registration Form Styling */
.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    display: block;
    font-size: 14px;
}

.form-group {
    margin-bottom: 20px;
}

.input-style {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.input-style:focus {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
    outline: none;
}

.input-style.is-invalid {
    border-color: #dc3545;
}

.input-style.is-invalid:focus {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.error {
    color: #dc3545;
    font-size: 12px;
    margin-top: 5px;
    display: block;
}

/* Register page specific styling */
.register-container {
    background: #f8f9fa;
    min-height: 100vh;
    padding: 20px 0;
}

.register-form {
    background: white;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    padding: 40px;
    max-width: 500px;
    margin: 0 auto;
}

.register-form h1 {
    color: #333;
    font-weight: 700;
    margin-bottom: 30px;
}

.btn-register {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    padding: 12px 30px;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.btn-register:hover {
    background: linear-gradient(45deg, #218838, #1ba085);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

/* Admin Menu Item Styling */
.dropdown-menu li a[href="/admin/dashboard"] {
    color: #28a745 !important;
    font-weight: bold;
    transition: all 0.3s ease;
}

.dropdown-menu li a[href="/admin/dashboard"]:hover {
    background-color: #f8f9fa !important;
    color: #1e7e34 !important;
}

.dropdown-menu li a[href="/admin/dashboard"] i {
    color: #28a745;
    margin-right: 8px;
}

/* Divider styling for admin section */
.dropdown-menu .divider {
    height: 1px;
    margin: 5px 0;
    overflow: hidden;
    background-color: #e5e5e5;
}

/* Improve dropdown menu spacing */
.dropdown-menu li a {
    padding: 8px 20px;
    line-height: 1.********;
}

/* User dropdown menu improvements */
.dropdown-menu {
    min-width: 180px;
    border-radius: 4px;
    box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

/* My Account Page Styling */
.my-account-box-main .card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
}

.my-account-box-main .card-header {
    border-radius: 8px 8px 0 0 !important;
    background: linear-gradient(45deg, #007bff, #0056b3) !important;
}

.my-account-box-main .card-body p {
    margin-bottom: 15px;
    font-size: 16px;
    line-height: 1.5;
}

.my-account-box-main .card-body strong {
    color: #333;
    font-weight: 600;
}

.my-account-box-main .badge {
    font-size: 12px;
    padding: 6px 12px;
}

/* Account service boxes improvements */
.account-box {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.account-box:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.service-box {
    padding: 30px 20px;
}

.service-icon a {
    display: inline-block;
    width: 70px;
    height: 70px;
    line-height: 70px;
    text-align: center;
    border-radius: 50%;
    margin-bottom: 20px;
}

.service-desc h4 {
    color: #333;
    margin-bottom: 10px;
}

.service-desc p {
    color: #666;
    font-size: 14px;
}

/* Cart Dropdown Global Styles - Override any conflicts */
.attr-nav .cart-dropdown {
    position: relative !important;
    display: inline-block !important;
}

.attr-nav .cart-dropdown-menu,
#cart-dropdown {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    width: 350px !important;
    background: white !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    z-index: 10000 !important;
    max-height: 400px !important;
    overflow-y: auto !important;
    margin: 5px 0 0 0 !important;
    padding: 0 !important;
    float: none !important;
    clear: none !important;
    display: none !important;
    opacity: 0 !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    list-style: none !important;
    text-align: left !important;
}

.attr-nav .cart-dropdown-menu.show,
#cart-dropdown.show,
.cart-dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
    visibility: visible !important;
}

/* Override any Bootstrap or framework styles */
.navbar .attr-nav .cart-dropdown-menu,
.navbar .attr-nav #cart-dropdown,
.navbar #cart-dropdown,
.navbar .cart-dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    width: 350px !important;
    background: white !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    z-index: 10000 !important;
    margin: 5px 0 0 0 !important;
    padding: 0 !important;
    list-style: none !important;
    text-align: left !important;
    float: none !important;
    display: none !important;
}

.navbar .attr-nav .cart-dropdown-menu.show,
.navbar .attr-nav #cart-dropdown.show,
.navbar #cart-dropdown.show,
.navbar .cart-dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    transform: translateY(0) !important;
    pointer-events: auto !important;
}

/* Ultra-aggressive cart dropdown fixes */
.attr-nav li.cart-dropdown,
.attr-nav .side-menu.cart-dropdown,
li.side-menu.cart-dropdown {
    position: relative !important;
    display: inline-block !important;
    list-style: none !important;
    float: none !important;
    width: auto !important;
}

.attr-nav .cart-dropdown-menu,
.attr-nav #cart-dropdown,
#cart-dropdown,
.cart-dropdown-menu {
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    width: 350px !important;
    min-width: 350px !important;
    max-width: 350px !important;
    background: white !important;
    border: 1px solid #ddd !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    z-index: 10000 !important;
    max-height: 400px !important;
    overflow-y: auto !important;
    margin: 5px 0 0 0 !important;
    padding: 0 !important;
    float: none !important;
    clear: none !important;
    list-style: none !important;
    text-align: left !important;
    display: none !important;
    opacity: 0 !important;
    transform: translateY(-10px) !important;
    transition: all 0.3s ease !important;
    pointer-events: none !important;
    vertical-align: top !important;
    white-space: normal !important;
}

/* Cart dropdown content styles */
.attr-nav .cart-dropdown-header,
#cart-dropdown .cart-dropdown-header {
    padding: 15px !important;
    border-bottom: 1px solid #eee !important;
    background: #f8f9fa !important;
    border-radius: 8px 8px 0 0 !important;
    margin: 0 !important;
    display: block !important;
}

.attr-nav .cart-dropdown-header h5,
#cart-dropdown .cart-dropdown-header h5 {
    margin: 0 !important;
    font-size: 16px !important;
    font-weight: 600 !important;
    color: #333 !important;
    display: block !important;
}

.attr-nav .cart-dropdown-body,
#cart-dropdown .cart-dropdown-body {
    max-height: 250px !important;
    overflow-y: auto !important;
    display: block !important;
    padding: 0 !important;
}

.attr-nav .cart-item,
#cart-dropdown .cart-item {
    display: flex !important;
    align-items: center !important;
    padding: 10px 15px !important;
    border-bottom: 1px solid #f0f0f0 !important;
    margin: 0 !important;
    width: 100% !important;
    box-sizing: border-box !important;
    list-style: none !important;
}

/* Cart message animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Homepage slider fixes */
#slides-shop {
    visibility: visible !important;
    opacity: 1 !important;
    height: 100vh !important;
    min-height: 500px !important;
}

#slides-shop .slides-container {
    height: 100% !important;
}

#slides-shop .slides-container li {
    height: 100vh !important;
    min-height: 500px !important;
    background-size: cover !important;
    background-position: center !important;
    background-repeat: no-repeat !important;
}

#slides-shop .slides-container li img {
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
}

/* Ensure slider content is visible */
.cover-slides {
    height: 100vh !important;
    min-height: 500px !important;
    position: relative !important;
    overflow: hidden !important;
}

.cover-slides .container {
    height: 100% !important;
    position: relative !important;
    z-index: 2 !important;
}

.cover-slides .container > .row {
    height: 100% !important;
    align-items: center !important;
    display: flex !important;
}

/* Order Detail Modal Styling */
.order-detail-modal {
    max-width: 95% !important;
    width: 95% !important;
}

.order-detail-modal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.order-detail-modal .modal-header {
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    border-radius: 12px 12px 0 0;
    padding: 20px 30px;
}

.order-detail-modal .modal-title {
    font-size: 1.5rem;
    font-weight: 600;
}

.order-detail-modal .modal-body {
    padding: 30px;
    max-height: 80vh;
    overflow-y: auto;
}

.order-detail-modal .modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e9ecef;
}

/* Order detail cards styling */
.order-detail-modal .card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 20px;
}

.order-detail-modal .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
}

.order-detail-modal .table {
    margin-bottom: 0;
}

.order-detail-modal .table td {
    padding: 12px 15px;
    border-top: 1px solid #e9ecef;
}

.order-detail-modal .table td:first-child {
    font-weight: 600;
    color: #495057;
    width: 30%;
}

/* Product table in order details */
.order-detail-modal .table thead th {
    background-color: #007bff;
    color: white;
    border: none;
    font-weight: 600;
    padding: 15px;
}

.order-detail-modal .table tbody td {
    padding: 15px;
    vertical-align: middle;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .order-detail-modal {
        max-width: 98% !important;
        width: 98% !important;
        margin: 10px auto;
    }

    .order-detail-modal .modal-body {
        padding: 20px;
        max-height: 70vh;
    }
}