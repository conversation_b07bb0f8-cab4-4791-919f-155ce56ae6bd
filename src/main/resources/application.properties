# Application name
spring.application.name=CDWeb-FreshShop

# Thymeleaf template engine configuration
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.cache=false
spring.thymeleaf.enabled=true

# MySQL Database connection settings
# Use environment variables for production security
spring.datasource.url=${DB_URL:**********************************************************************************************}
spring.datasource.username=${DB_USERNAME:root}
spring.datasource.password=${DB_PASSWORD:11111111}
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# HikariCP connection pool configuration
spring.datasource.hikari.minimum-idle=5
spring.datasource.hikari.maximum-pool-size=10
spring.datasource.hikari.idle-timeout=30000
spring.datasource.hikari.pool-name=HikariPool-1
spring.datasource.hikari.max-lifetime=1800000
spring.datasource.hikari.connection-timeout=30000

# JPA and Hibernate settings
spring.jpa.show-sql=${SHOW_SQL:false}
spring.jpa.properties.hibernate.format_sql=false
spring.jpa.hibernate.ddl-auto=${DDL_AUTO:update}
spring.jpa.open-in-view=false
# Note: Hibernate auto-detects MySQL dialect, explicit setting not needed

# Logging
logging.level.vn.fshop=INFO

# Server settings
server.error.include-message=always
server.error.include-binding-errors=always