package vn.fshop.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import vn.fshop.model.User;
import vn.fshop.model.UserRoleEnum;
import vn.fshop.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;

@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    public User login(String username, String password) {
        try {
            if (username == null || username.trim().isEmpty() || password == null || password.trim().isEmpty()) {
                logger.warn("Login attempt with empty username or password");
                return null;
            }

            User user = userRepository.findByUsername(username.trim()).orElse(null);
            if (user != null && passwordEncoder.matches(password, user.getPassword())) {
                logger.info("Successful login for user: {}", username);
                return user;
            }
            logger.warn("Failed login attempt for user: {}", username);
            return null;
        } catch (Exception e) {
            logger.error("Error during login for user: {}", username, e);
            return null;
        }
    }

    public User register(String username, String password, String firstname, String lastname,
                        String phone, String email, String address) {
        try {
            // Input validation
            if (username == null || username.trim().isEmpty()) {
                throw new IllegalArgumentException("Username cannot be empty");
            }
            if (password == null || password.length() < 6) {
                throw new IllegalArgumentException("Password must be at least 6 characters");
            }
            if (email == null || email.trim().isEmpty()) {
                throw new IllegalArgumentException("Email cannot be empty");
            }

            // Trim inputs
            username = username.trim();
            email = email.trim();
            firstname = firstname != null ? firstname.trim() : "";
            lastname = lastname != null ? lastname.trim() : "";
            phone = phone != null ? phone.trim() : "";
            address = address != null ? address.trim() : "";

            if (userRepository.existsByUsername(username)) {
                throw new RuntimeException("Username already exists");
            }
            if (userRepository.existsByEmail(email)) {
                throw new RuntimeException("Email already exists");
            }

            // Encrypt password before saving
            String encryptedPassword = passwordEncoder.encode(password);
            User user = new User(username, encryptedPassword, firstname, lastname, phone, email, address);

            User savedUser = userRepository.save(user);
            logger.info("New user registered: {}", username);
            return savedUser;
        } catch (Exception e) {
            logger.error("Error during user registration for username: {}", username, e);
            throw e;
        }
    }

    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    public boolean existsByUsername(String username) {
        return userRepository.existsByUsername(username);
    }

    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    public Optional<User> findById(Integer id) {
        return userRepository.findById(id);
    }

    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    public User saveUser(User user) {
        return userRepository.save(user);
    }

    public boolean isAdmin(User user) {
        return user != null && user.getRole() == UserRoleEnum.ADMIN;
    }

    public void promoteToAdmin(String username) {
        User user = findByUsername(username).orElseThrow(() ->
            new RuntimeException("User not found: " + username));
        user.setRole(UserRoleEnum.ADMIN);
        saveUser(user);
    }

    // Create admin user if none exists
    public void createDefaultAdminIfNotExists() {
        try {
            // Check if any admin user exists
            List<User> allUsers = getAllUsers();
            boolean hasAdmin = allUsers.stream().anyMatch(this::isAdmin);

            if (!hasAdmin) {
                // Create default admin user with encrypted password
                String encryptedPassword = passwordEncoder.encode("admin");
                User admin = new User("admin", encryptedPassword, "Admin", "User", "0123456789", "<EMAIL>", "Admin Address");
                admin.setRole(UserRoleEnum.ADMIN);
                saveUser(admin);
                logger.info("Default admin user created");
            }
        } catch (Exception e) {
            logger.error("Error creating default admin user", e);
        }
    }

    /**
     * Update user password with encryption
     */
    public void updatePassword(User user, String newPassword) {
        try {
            if (newPassword == null || newPassword.length() < 6) {
                throw new IllegalArgumentException("Password must be at least 6 characters");
            }
            user.setPassword(passwordEncoder.encode(newPassword));
            saveUser(user);
            logger.info("Password updated for user: {}", user.getUsername());
        } catch (Exception e) {
            logger.error("Error updating password for user: {}", user.getUsername(), e);
            throw e;
        }
    }

    /**
     * Verify if a password matches the stored encrypted password
     */
    public boolean verifyPassword(User user, String password) {
        try {
            return passwordEncoder.matches(password, user.getPassword());
        } catch (Exception e) {
            logger.error("Error verifying password for user: {}", user.getUsername(), e);
            return false;
        }
    }


}
